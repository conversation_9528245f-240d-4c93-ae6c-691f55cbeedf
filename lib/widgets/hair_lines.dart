import 'dart:math';
import 'eye_shape.dart'; // For Point2D and RandomUtils

/// Utility class for Bezier curve calculations
class BezierCurveUtils {
  /// Calculate factorial
  static int factorial(int n) {
    if (n <= 1) return 1;
    return n * factorial(n - 1);
  }
  
  /// Calculate binomial coefficient
  static int binomialCoefficient(int n, int k) {
    return factorial(n) ~/ (factorial(k) * factorial(n - k));
  }
  
  /// Calculate a point on a Bezier curve
  static Point2D calculateBezierPoint(double t, List<Point2D> controlPoints) {
    double x = 0, y = 0;
    final int n = controlPoints.length - 1;
    
    for (int i = 0; i <= n; i++) {
      final int binCoeff = binomialCoefficient(n, i);
      final double a = pow(1 - t, n - i).toDouble();
      final double b = pow(t, i).toDouble();
      x += binCoeff * a * b * controlPoints[i].x;
      y += binCoeff * a * b * controlPoints[i].y;
    }
    
    return Point2D(x, y);
  }
  
  /// Compute a complete Bezier curve
  static List<Point2D> computeBezierCurve(List<Point2D> controlPoints, int numberOfPoints) {
    final List<Point2D> curve = [];
    for (int i = 0; i <= numberOfPoints; i++) {
      final double t = i / numberOfPoints;
      final Point2D point = calculateBezierPoint(t, controlPoints);
      curve.add(point);
    }
    return curve;
  }
}

/// Utility class for generating hair lines based on face contour
class HairLinesGenerator {
  /// Generate hair lines method 0 - Blended Bezier curves
  static List<List<Point2D>> generateHairLines0(List<Point2D> faceContour, {int numHairLines = 100}) {
    if (faceContour.length < 3) return [];
    
    // Remove the last 2 points (as in the original JS)
    final List<Point2D> faceContourCopy = faceContour.sublist(0, faceContour.length - 2);
    final List<List<Point2D>> results = [];
    
    for (int i = 0; i < numHairLines; i++) {
      final int numHairPoints = 15 + (RandomUtils.randomFromInterval(-3, 3)).round(); // Reduced hair points

      // Generate first hair line - limit to upper face area
      final List<Point2D> hairLine1 = [];
      final int indexOffset1 = (RandomUtils.randomFromInterval(40, 120)).round(); // More focused range

      for (int j = 0; j < numHairPoints; j++) {
        final int index = (faceContourCopy.length - (j + indexOffset1)) % faceContourCopy.length;
        // Scale down the hair points to keep them closer to the head
        final Point2D originalPoint = faceContourCopy[index];
        final double scaleFactor = 0.9 + (j / numHairPoints) * 0.3; // Gradual scaling from 0.9 to 1.2
        hairLine1.add(Point2D(
          originalPoint.x * scaleFactor,
          originalPoint.y * scaleFactor,
        ));
      }

      // Increase point density for smoother curves
      final int curvePoints = numHairPoints * 2; // Double the points for smoother curves
      final List<Point2D> d0 = BezierCurveUtils.computeBezierCurve(hairLine1, curvePoints);

      // Generate second hair line - limit to upper face area
      final List<Point2D> hairLine2 = [];
      final int indexOffset2 = (RandomUtils.randomFromInterval(40, 120)).round(); // More focused range

      for (int j = 0; j < numHairPoints; j++) {
        final int index = (faceContourCopy.length - (-j + indexOffset2)) % faceContourCopy.length;
        // Scale down the hair points to keep them closer to the head
        final Point2D originalPoint = faceContourCopy[index];
        final double scaleFactor = 0.9 + (j / numHairPoints) * 0.3; // Gradual scaling from 0.9 to 1.2
        hairLine2.add(Point2D(
          originalPoint.x * scaleFactor,
          originalPoint.y * scaleFactor,
        ));
      }

      final List<Point2D> d1 = BezierCurveUtils.computeBezierCurve(hairLine2, curvePoints);

      // Blend the two curves
      final List<Point2D> blendedCurve = [];
      for (int j = 0; j < curvePoints; j++) {
        final double t = j / curvePoints;
        final double weight = pow(t, 2).toDouble();
        final double x = d0[j].x * weight + d1[j].x * (1 - weight);
        final double y = d0[j].y * weight + d1[j].y * (1 - weight);
        blendedCurve.add(Point2D(x, y));
      }

      results.add(blendedCurve);
    }
    
    return results;
  }
  
  /// Generate hair lines method 1 - Random control points
  static List<List<Point2D>> generateHairLines1(List<Point2D> faceContour, {int numHairLines = 100}) {
    if (faceContour.length < 3) return [];
    
    final List<Point2D> faceContourCopy = faceContour.sublist(0, faceContour.length - 2);
    final List<List<Point2D>> results = [];
    
    for (int i = 0; i < numHairLines; i++) {
      final int numHairPoints = 15 + (RandomUtils.randomFromInterval(-3, 3)).round(); // Reduced hair points
      final List<Point2D> hairLine = [];

      // Start point - limit to upper face area
      int indexStart = (RandomUtils.randomFromInterval(30, 130)).round(); // More focused range
      final int startIndex = (faceContourCopy.length - indexStart) % faceContourCopy.length;
      final Point2D startPoint = faceContourCopy[startIndex];
      hairLine.add(Point2D(startPoint.x * 0.95, startPoint.y * 0.95)); // Scale down slightly

      // Add random control points with scaling
      for (int j = 1; j < numHairPoints + 1; j++) {
        indexStart = (RandomUtils.randomFromInterval(30, 130)).round(); // More focused range
        final int index = (faceContourCopy.length - indexStart) % faceContourCopy.length;
        final Point2D originalPoint = faceContourCopy[index];
        final double scaleFactor = 0.9 + (j / numHairPoints) * 0.4; // Gradual scaling from 0.9 to 1.3
        hairLine.add(Point2D(
          originalPoint.x * scaleFactor,
          originalPoint.y * scaleFactor,
        ));
      }

      // Increase point density for smoother curves
      final int curvePoints = numHairPoints * 2; // Double the points for smoother curves
      final List<Point2D> curve = BezierCurveUtils.computeBezierCurve(hairLine, curvePoints);
      results.add(curve);
    }
    
    return results;
  }
  
  /// Generate hair lines method 2 - Scaled and connected curves
  static List<List<Point2D>> generateHairLines2(List<Point2D> faceContour, {int numHairLines = 100}) {
    if (faceContour.length < 3) return [];

    final List<Point2D> faceContourCopy = faceContour.sublist(0, faceContour.length - 2);
    final List<List<Point2D>> results = [];

    // Pick and sort indices - limit to upper part of face for hair
    final List<int> pickedIndices = [];
    for (int i = 0; i < numHairLines; i++) {
      // Restrict hair to upper 60% of face contour (roughly forehead area)
      pickedIndices.add((RandomUtils.randomFromInterval(20, 120)).round());
    }
    pickedIndices.sort();

    for (int i = 0; i < numHairLines; i++) {
      final int numHairPoints = 15 + (RandomUtils.randomFromInterval(-3, 3)).round(); // Reduced hair points
      final List<Point2D> hairLine = [];
      final int indexOffset = pickedIndices[i];
      // Reduce hair length by limiting the lower bound
      final double lower = RandomUtils.randomFromInterval(0.9, 1.2); // More conservative scaling
      final int reverse = Random().nextBool() ? 1 : -1;

      for (int j = 0; j < numHairPoints; j++) {
        final double powerScale = RandomUtils.randomFromInterval(0.3, 1.5); // Less extreme scaling
        final double portion = (1 - pow(j / numHairPoints, powerScale)) * (1 - lower) + lower;
        // Limit portion to prevent hair from extending too far
        final double clampedPortion = portion.clamp(0.7, 1.3);
        final int index = (faceContourCopy.length - (reverse * j + indexOffset)) % faceContourCopy.length;

        hairLine.add(Point2D(
          faceContourCopy[index].x * clampedPortion,
          faceContourCopy[index].y * clampedPortion,
        ));
      }

      // Increase point density for smoother curves
      final int curvePoints = numHairPoints * 2; // Double the points for smoother curves
      List<Point2D> curve = BezierCurveUtils.computeBezierCurve(hairLine, curvePoints);
      if (Random().nextDouble() > 0.7) {
        curve = curve.reversed.toList();
      }

      if (results.isEmpty) {
        results.add(curve);
        continue;
      }

      // Check if we should connect to the last hair line
      final Point2D lastHairPoint = results.last.last;
      final double distance = sqrt(
        pow(curve.first.x - lastHairPoint.x, 2) + pow(curve.first.y - lastHairPoint.y, 2)
      );

      if (Random().nextDouble() > 0.5 && distance < 50) { // Reduced connection distance
        results.last.addAll(curve);
      } else {
        results.add(curve);
      }
    }

    return results;
  }
  
  /// Generate hair lines method 3 - Split and directional curves
  static List<List<Point2D>> generateHairLines3(List<Point2D> faceContour, {int numHairLines = 100}) {
    if (faceContour.length < 3) return [];

    final List<Point2D> faceContourCopy = faceContour.sublist(0, faceContour.length - 2);
    final List<List<Point2D>> results = [];

    // Pick and sort indices - focus on hair area
    final List<int> pickedIndices = [];
    for (int i = 0; i < numHairLines; i++) {
      // Limit to upper portion of face for more realistic hair placement
      pickedIndices.add((RandomUtils.randomFromInterval(30, 150)).round());
    }
    pickedIndices.sort();

    final int splitPoint = (RandomUtils.randomFromInterval(60, 120)).round(); // More centered split

    for (int i = 0; i < numHairLines; i++) {
      final int numHairPoints = 20 + (RandomUtils.randomFromInterval(-5, 5)).round(); // Reduced points
      final List<Point2D> hairLine = [];
      final int indexOffset = pickedIndices[i];

      // More conservative hair length scaling
      double lower = RandomUtils.randomFromInterval(0.8, 1.4);
      if (Random().nextDouble() > 0.9) {
        lower = RandomUtils.randomFromInterval(0.7, 1.0);
      }

      final int reverse = indexOffset > splitPoint ? 1 : -1;

      for (int j = 0; j < numHairPoints; j++) {
        final double powerScale = RandomUtils.randomFromInterval(0.3, 1.8); // Less extreme scaling
        final double portion = (1 - pow(j / numHairPoints, powerScale)) * (1 - lower) + lower;
        // Clamp portion to prevent excessive hair length
        final double clampedPortion = portion.clamp(0.6, 1.5);
        final int index = (faceContourCopy.length - (reverse * j + indexOffset)) % faceContourCopy.length; // Reduced step size

        hairLine.add(Point2D(
          faceContourCopy[index].x * clampedPortion,
          faceContourCopy[index].y * (clampedPortion * 0.9), // Slightly compress Y to keep hair closer to head
        ));
      }

      // Increase point density for smoother curves
      final int curvePoints = numHairPoints * 2; // Double the points for smoother curves
      final List<Point2D> curve = BezierCurveUtils.computeBezierCurve(hairLine, curvePoints);
      results.add(curve);
    }

    return results;
  }
  
  /// Generate random hair using a combination of methods (similar to Vue component)
  static List<List<Point2D>> generateRandomHair(List<Point2D> faceContour) {
    final List<List<Point2D>> allHairLines = [];
    // Reduce the number of hair lines to prevent overcrowding
    final List<int> numHairLines = List.generate(4, (index) =>
        (RandomUtils.randomFromInterval(5, 25)).round()); // Reduced from 0-50 to 5-25

    // Randomly apply different hair generation methods with reduced probability
    if (Random().nextDouble() > 0.4) { // Reduced from 0.3 to 0.4
      allHairLines.addAll(generateHairLines0(faceContour, numHairLines: numHairLines[0] + 5)); // Reduced base
    }

    if (Random().nextDouble() > 0.4) { // Reduced from 0.3 to 0.4
      allHairLines.addAll(generateHairLines1(faceContour, numHairLines: (numHairLines[1] / 1.5).round() + 5)); // Reduced base
    }

    if (Random().nextDouble() > 0.6) { // Reduced from 0.5 to 0.6
      allHairLines.addAll(generateHairLines2(faceContour, numHairLines: numHairLines[2] * 2 + 5)); // Reduced multiplier and base
    }

    if (Random().nextDouble() > 0.6) { // Reduced from 0.5 to 0.6
      allHairLines.addAll(generateHairLines3(faceContour, numHairLines: numHairLines[3] * 2 + 5)); // Reduced multiplier and base
    }

    return allHairLines;
  }
}
